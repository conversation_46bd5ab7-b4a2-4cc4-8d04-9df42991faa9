const mongoose = require('mongoose');
const { TTS_CORE_COMPUTE_PLATFORM } = require('../constants');

const requestContentSchema = new mongoose.Schema(
  {
    _id: String,
    text: String,
    sentences: [
      {
        _id: false,
        text: String,
        voiceCode: String,
        speed: Number,
        breakTime: Number,
        characters: Number,
        synthesisComputePlatform: {
          type: String,
          enum: Object.values(TTS_CORE_COMPUTE_PLATFORM),
        },
      },
    ],
  },
  {
    _id: false,
    timestamps: true,
    versionKey: false,
  },
);

module.exports = mongoose.model('RequestContent', requestContentSchema);

const RequestContent = require('../models/requestContent');

const createRequestContent = async ({ requestId, text, sentences }) => {
  const requestContent = await RequestContent.create({
    _id: requestId,
    text,
    sentences,
  });

  return requestContent;
};

const deleteRequestContent = async (id) => {
  await RequestContent.findByIdAndDelete(id);
};

const getRequestContent = async (id) => {
  const requestContent = await RequestContent.findById(id).lean();
  return requestContent;
};

const getDetailSentencesByRequestContentId = async (requestId) => {
  const [request] = await RequestContent.aggregate([
    { $match: { _id: requestId } },
    { $unwind: '$sentences' },
    {
      $lookup: {
        from: 'voices',
        localField: 'sentences.voiceCode',
        foreignField: 'code',
        as: 'sentences.voice',
      },
    },
    { $unwind: '$sentences.voice' },
    {
      $lookup: {
        from: 'languages',
        localField: 'sentences.voice.languageCode',
        foreignField: 'code',
        as: 'sentences.voice.language',
      },
    },
    { $unwind: '$sentences.voice.language' },
    {
      $group: {
        _id: '$_id',
        sentences: { $push: '$sentences' },
      },
    },
  ]);

  return request.sentences;
};

module.exports = {
  createRequestContent,
  deleteRequestContent,
  getRequestContent,
  getDetailSentencesByRequestContentId,
};

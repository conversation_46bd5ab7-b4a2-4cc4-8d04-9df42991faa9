const {
  getSearchQuery,
  getSortQuery,
  getSelectQ<PERSON>y,
  getDateQuery,
} = require('./util');

const findAll = async (
  model,
  { search, searchFields = [], query, offset, limit, fields, sort },
) => {
  const s = getSearchQuery(model, searchFields, search);

  query = getDateQuery(query);

  const total = await model.countDocuments(
    search ? { $or: s, ...query } : query,
  );

  const documents = await model
    .find(search ? { $or: s, ...query } : query)
    .skip(offset || 0)
    .limit(limit || null)
    .sort(getSortQuery(sort))
    .select(fields ? getSelectQuery(fields) : {})
    .lean();

  return { documents, total };
};

module.exports = { findAll };

const snakecaseKeys = require('snakecase-keys');
const codes = require('../errors/code');
const getErrorMessage = require('../errors/message');

// eslint-disable-next-line no-unused-vars
const errorHandler = (err, req, res, next) => {
  let statusCode = err.errorCode || err.statusCode;
  let errorMessage;
  let details;
  const errorCode =
    err.errorCode || err.statusCode || codes.INTERNAL_SERVER_ERROR;
  switch (errorCode) {
    case codes.BAD_REQUEST:
      errorMessage = 'Bad Request';
      details = err.details;
      break;
    case codes.UNAUTHORIZED:
      errorMessage = 'Unauthorized';
      break;
    case codes.FORBIDDEN:
      errorMessage = 'Forbidden';
      break;
    case codes.NOT_FOUND:
      errorMessage = 'Not Found';
      break;
    case codes.TOO_MANY_REQUESTS:
      errorMessage = 'Too many requests';
      break;
    case codes.INTERNAL_SERVER_ERROR:
      statusCode = codes.INTERNAL_SERVER_ERROR;
      errorMessage = 'Something went wrong';
      break;
    case codes.USER_BLOCK:
      errorMessage = 'User has been locked';
      break;
    default:
      errorMessage = getErrorMessage(errorCode);
      statusCode = 200;
  }
  errorMessage = err.message || errorMessage;
  const originalUrl = req.baseUrl ? `${req.baseUrl}${req.url}` : req.url;
  err.message = `${req.method.toUpperCase()} - ${originalUrl} - ${errorMessage}`;
  logger.error(err);

  return res.status(statusCode).send(
    snakecaseKeys(
      {
        status: 0,
        errorCode,
        errorMessage,
        details,
      },
      { deep: true },
    ),
  );
};

module.exports = errorHandler;

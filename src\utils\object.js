const convertObjectIdToString = (obj) => {
  if (typeof obj !== 'object') {
    return obj;
  }

  // typeof null = object
  if (obj === null) {
    return null;
  }

  // Check if obj is Mongoose Object
  if (obj._doc) {
    return convertObjectIdToString(obj.toJSON());
  }

  // Check if obj is ObjectId
  if (obj._bsontype === 'ObjectID' || obj._bsontype === 'ObjectId') {
    return obj.toString();
  }

  Object.keys(obj).forEach((key) => {
    // eslint-disable-next-line no-param-reassign
    obj[key] = convertObjectIdToString(obj[key]);
  });

  if (Array.isArray(obj)) return obj;
  if (Object.prototype.toString.call(obj) === '[object Date]') return obj;

  return obj;
};

const normalizeObject = (obj) => convertObjectIdToString(obj);

const splitArray = (arr, size) =>
  arr.reduce((result, value, i) => {
    if (i % size === 0) result.push([]);
    result[result.length - 1].push(value);
    return result;
  }, []);

module.exports = { normalizeObject, splitArray };

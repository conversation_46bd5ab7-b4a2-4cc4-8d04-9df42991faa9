const { REGEX } = require('../constants');

const removeHtmlTags = (text) => {
  const cleanText = text.replace(REGEX.HTML, ' ').toLowerCase().trim();
  return cleanText;
};

// Remove special space characters
const removeSpecialCharacters = (str) => {
  const specialCharacters = ['\\u200B'];

  const specialCharactersRegex = new RegExp(specialCharacters.join('|'), 'g');
  return str.replace(specialCharactersRegex, '');
};

module.exports = { removeHtmlTags, removeSpecialCharacters };

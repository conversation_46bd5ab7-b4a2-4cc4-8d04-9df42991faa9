/* eslint-disable array-callback-return */
require('dotenv').config();
const snakeCaseKeys = require('snakecase-keys');

const kafka = require('./kafka');
const { normalizeObject } = require('../../utils/object');
const { LOG_SHORTEN } = require('../../configs');

const producer = kafka.producer();
producer.connect();

const sendMessage = async (topic, message) => {
  try {
    message = normalizeObject(message);
    message = snakeCaseKeys(message, { deep: true });
    message.value = JSON.stringify(message.value);

    const result = await producer.send({ topic, messages: [message] });

    logger.info(
      JSON.stringify(LOG_SHORTEN ? { topic } : { topic, message, result }),
      {
        ctx: 'KafkaProduce',
      },
    );
  } catch (error) {
    logger.error(error, { ctx: 'KafkaProduce' });
  }
};

const sendMessages = async (topic, messages = []) => {
  try {
    messages = normalizeObject(messages);
    messages = snakeCaseKeys(messages, { deep: true });
    messages = messages.map((message) => ({
      ...message,
      value: JSON.stringify(message.value),
    }));

    const result = await producer.send({ topic, messages });
    logger.info(JSON.stringify({ topic, messages, result }), {
      ctx: 'KafkaProduce',
    });
  } catch (error) {
    logger.error(error, { ctx: 'KafkaProduce' });
  }
};

const run = async () => {
  try {
    await producer.connect();
  } catch (error) {
    logger.error(error, { ctx: 'KafkaProduce' });
  }
};

run().catch((e) => {
  logger.error(e, { ctx: 'KafkaProduce' });
});

const errorTypes = ['unhandledRejection', 'uncaughtException'];
const signalTraps = ['SIGTERM', 'SIGINT', 'SIGUSR2'];

errorTypes.map((type) => {
  process.on(type, async (e) => {
    try {
      logger.info(`process.on ${type}`);
      logger.error(e);
      await producer.disconnect();
      process.exit(0);
    } catch (_) {
      process.exit(1);
    }
  });
});

signalTraps.map((type) => {
  process.once(type, async () => {
    try {
      await producer.disconnect();
    } finally {
      process.kill(process.pid, type);
    }
  });
});

module.exports = { sendMessage, sendMessages };

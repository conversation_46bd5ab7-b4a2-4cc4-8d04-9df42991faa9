const router = require('express').Router();
const asyncMiddleware = require('../middlewares/async');
const dubbingController = require('../controllers/dubbing');
const { dubbingApiValidation } = require('../validations/dubbing');
const { authAPI } = require('../middlewares/auth');

/* eslint-disable prettier/prettier */
router.post('/dubbing', authAPI, dubbingApiValidation, asyncMiddleware(dubbingController.handleDubbing));
/* eslint-disable prettier/prettier */


module.exports = router;

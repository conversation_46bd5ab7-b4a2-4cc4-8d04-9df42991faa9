const { REG<PERSON>, ACRONYM_LINKS, WORD_LINKS } = require('../constants');
const FEATURE_KEYS = require('../constants/featureKeys');
const { getFeatureValue } = require('./growthbook');

const detectAcronyms = (paragraph) => {
  try {
    const matches = paragraph.matchAll(REGEX.ACRONYM);
    const result = {};

    let modifiedText = paragraph;

    for (const match of matches) {
      const phrase = match[1]?.trim();
      const acronym = match[2]?.trim();
      const normalizedAcronym = acronym
        .replace(REGEX.AND_CHARACTER, '')
        .replace(/\s+/g, '');

      const phraseParts = phrase.split(/\s+/);
      const matchingPhrase = [];
      let acronymIndex = normalizedAcronym.length - 1;

      for (let i = phraseParts.length - 1; i >= 0; i -= 1) {
        const word = phraseParts[i];
        const firstLetter = word[0]?.toUpperCase();

        if (normalizedAcronym[acronymIndex] === firstLetter) {
          matchingPhrase.unshift(word);
          acronymIndex -= 1;
        } else if (ACRONYM_LINKS.includes(normalizedAcronym[acronymIndex])) {
          acronymIndex -= 1;
          i += 1;
        } else if (WORD_LINKS.includes(word)) {
          matchingPhrase.unshift(word);
        }

        if (acronymIndex < 0) break;
      }

      if (acronymIndex < 0) {
        result[acronym] = matchingPhrase.join(' ').trim();
        const regexAcronymWithParentheses = new RegExp(`\\(${acronym}\\)`, 'g');
        modifiedText = modifiedText.replace(regexAcronymWithParentheses, '');
      }
    }

    modifiedText = modifiedText.replace(/\s{2,}/g, ' ').trim();

    return {
      modifiedText,
      detectedAcronyms: result,
    };
  } catch (error) {
    return {
      modifiedText: paragraph,
      detectedAcronyms: {},
    };
  }
};

const updateDictionaryAndTextWithAcronyms = ({
  requestId,
  text,
  dictionary = [],
  clientUserId,
  voiceProvider,
  appId,
}) => {
  const dictionaryWithAcronyms = [...dictionary];
  let outputText = text;
  const useAcronymInRequest = getFeatureValue(FEATURE_KEYS.ACRONYM_IN_REQUEST, {
    userId: clientUserId,
    voiceProvider,
    appId,
  });

  if (useAcronymInRequest) {
    const { acronyms, modifiedText } = detectAcronyms(text).detectedAcronyms;
    outputText = modifiedText;

    logger.info(
      { oldDictionary: dictionary, acronyms, requestId },
      { ctx: 'UpdateDictionaryAndTextWithAcronyms' },
    );

    for (const [acronym, pronunciation] of Object.entries(acronyms)) {
      const existingIndex = dictionaryWithAcronyms.findIndex(
        (item) => item.word === acronym,
      );

      if (existingIndex !== -1)
        dictionaryWithAcronyms[existingIndex].pronunciation = pronunciation;
      else dictionaryWithAcronyms.push({ word: acronym, pronunciation });
    }
  }

  return { dictionaryWithAcronyms, text: outputText };
};

module.exports = { detectAcronyms, updateDictionaryAndTextWithAcronyms };

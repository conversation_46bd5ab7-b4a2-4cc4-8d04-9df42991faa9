const apiSynthesisService = require('../services/apiSynthesis');

const apiCallbackResponse = async (req, res) => {
  const {
    requestId: ttsRequestId,
    sessionId: requestId,
    status,
    audioLink,
    errorCode,
    errorMessage,
    timestampWords,
  } = req.body;

  const response = await apiSynthesisService.apiCallbackResponse({
    requestId,
    ttsRequestId,
    status,
    audioLink,
    errorCode,
    errorMessage,
    timestampWords,
  });

  return res.send(response);
};

module.exports = { apiCallbackResponse };

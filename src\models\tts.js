const mongoose = require('mongoose');
const { AUDIO_TYPE, TTS_STATUS } = require('../constants');

const ttsSchema = new mongoose.Schema(
  {
    _id: String,
    requestId: { type: String, required: true, ref: 'Request' },
    index: { type: Number, required: true },
    subIndex: Number,
    text: { type: String, required: true },
    voiceCode: { type: String, required: true, ref: 'Voice' },
    audioLink: String,
    silence: { type: Boolean, default: false },
    phrases: [
      {
        _id: false,
        index: Number,
        text: String,
        audioLink: String,
        a2aDuration: Number, // allophone to audio duration
        cache: Boolean, // is cache or not
        error: String,
      },
    ],
    audioType: {
      type: String,
      enum: Object.values(AUDIO_TYPE),
    },
    status: { type: String, required: true, enum: Object.values(TTS_STATUS) },
    error: String,
    t2aDuration: Number,
    synthesisDuration: Number,
    isPriority: { type: Boolean, default: false },
    coreT2ADuration: Number,
    prodT2ADuration: Number,
    coreSynthesisDuration: Number,
    prodSynthesisDuration: Number,
    workerPriorityType: String,
  },
  { _id: false, versionKey: false, timestamps: true },
);

module.exports = mongoose.model('Tts', ttsSchema);

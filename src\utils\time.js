const { TIME_DELTA_REGEX } = require('../constants');

const isValidTimeDelta = (timeDelta) => TIME_DELTA_REGEX.test(timeDelta);

// Convert time delta to milliseconds
const convertTimeDeltaToMillis = (timeDelta = '') => {
  const [hour, minute, secondPart] = timeDelta.split(':');
  const second = parseFloat(secondPart.replace(',', '.'));

  return (
    parseInt(hour, 10) * 3600 * 1000 +
    parseInt(minute, 10) * 60 * 1000 +
    second * 1000
  );
};

module.exports = { isValidTimeDelta, convertTimeDeltaToMillis };

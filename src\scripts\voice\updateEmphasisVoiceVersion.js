require('dotenv').config();
require('../../models');

const { TTS_CORE_VERSION } = require('../../constants');
const Voice = require('../../models/voice');
const logger = require('../../utils/logger');

global.logger = logger;

(async () => {
  logger.info(`Starting update version emphasis voice ...`, {
    ctx: 'UpdateEmphasisVoiceVersion',
  });

  await Voice.updateMany(
    {
      version: TTS_CORE_VERSION.NEW,
    },
    {
      $set: {
        version: TTS_CORE_VERSION.OLD,
      },
    },
  );

  logger.info(`Update version emphasis voice successfully`, {
    ctx: 'UpdateEmphasisVoiceVersion',
  });
  process.exit(1);
})();

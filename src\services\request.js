const moment = require('moment');
const requestDao = require('../daos/request');
const CustomError = require('../errors/CustomError');
const code = require('../errors/code');
const { createInProgressRequest } = require('../daos/inProgressRequest');
const { getSynthesisTimeInRedis } = require('../daos/tts');
const { updateRequestByIdInRedis } = require('../daos/request');
const { setKeyWithTtl, redisClient } = require('./redis');
const {
  REDIS_KEY_PREFIX,
  REDIS_KEY_TTL,
  VN_DOMAIN,
  REGEX,
  REQUEST_STATUS,
} = require('../constants');
const { nonAccentVietnamese } = require('../utils/accent');
const { renameS3File, getKeyFromS3Url } = require('./s3');
const { DEFAULT_BUCKET_S3, DEFAULT_AWS_REGION } = require('../configs');
const { createRequestContent } = require('../daos/requestContent');

const createRequest = async (requestInfo) => {
  requestInfo = { ...requestInfo };
  requestInfo._id = requestInfo.requestId;
  const { text, sentences } = requestInfo;
  delete requestInfo.requestId;
  delete requestInfo.text;

  const request = await requestDao.createRequest(requestInfo);

  const { _id, createdAt } = requestInfo;
  await createInProgressRequest(_id, createdAt);
  await createRequestContent({ requestId: _id, text, sentences });

  return request;
};

const createRequestInRedis = async (requestInfo) => {
  requestInfo = { ...requestInfo };
  if (!requestInfo._id) requestInfo._id = requestInfo.requestId;
  delete requestInfo.requestId;

  const requestKey = `${REDIS_KEY_PREFIX.REQUEST}_${requestInfo._id}`;
  const requestKeyTtl = REDIS_KEY_TTL.SYNTHESIS_REQUEST;

  await setKeyWithTtl(requestKey, JSON.stringify(requestInfo), requestKeyTtl);
};

const updateParagraphsOfRequest = async (requestId, paragraphs) => {
  const request = await requestDao.findRequestById(requestId);
  if (!request) throw new CustomError(code.BAD_REQUEST, 'Request is not found');

  const { paragraphs: paragraphsExists } = request;

  if (paragraphsExists === undefined) {
    await requestDao.updateRequestById(request._id, { paragraphs });
  }
};

const saveSynthesisTime = async (requestId, startTime) => {
  const { t2aDurations, synthesisDurations } = await getSynthesisTimeInRedis(
    requestId,
  );

  const getMinMaxAverageValueOfArray = (array) => {
    const min = array.length ? Math.min(...array) : 0;
    const max = array.length ? Math.max(...array) : 0;
    const arrayLength = array.length || 1;
    const average = array.reduce((a, b) => a + b, 0) / arrayLength;
    return { min, max, average: average.toFixed(3) };
  };
  const {
    min: minT2aDuration,
    max: maxT2aDuration,
    average: averageT2aDuration,
  } = getMinMaxAverageValueOfArray(t2aDurations);
  const {
    min: minSynthesisDuration,
    max: maxSynthesisDuration,
    average: averageSynthesisDuration,
  } = getMinMaxAverageValueOfArray(synthesisDurations);

  await updateRequestByIdInRedis(requestId, {
    t2aDuration: {
      min: minT2aDuration,
      max: maxT2aDuration,
      average: averageT2aDuration,
    },
    synthesisDuration: {
      min: minSynthesisDuration,
      max: maxSynthesisDuration,
      average: averageSynthesisDuration,
      start: startTime,
      end: Date.now(),
    },
  });
};

const getRequestFromSessionId = async (sessionId) => {
  const selectFields = [
    'app',
    'characters',
    'voiceCode',
    'audioType',
    'speed',
    'bitrate',
    'progress',
    'createdAt',
    'status',
    'audioLink',
    'retentionPeriod',
    'error',
    'errorCode',
  ];

  // Find the request in the database
  const requestDB = await requestDao.findRequest({ sessionId }, selectFields);

  // Throw an error if the request is not found
  if (!requestDB) throw new CustomError(code.REQUEST_NOT_FOUND);

  // If the request is in progress, get it from the cache
  const request =
    requestDB.status === REQUEST_STATUS.IN_PROGRESS
      ? await requestDao.findRequestByIdInRedis(requestDB._id)
      : requestDB;

  // Destructure the request object
  const {
    app: appId,
    characters,
    voiceCode,
    audioType,
    speed,
    bitrate,
    progress,
    createdAt,
    status,
    audioLink,
    retentionPeriod,
  } = request;

  // Construct the result object using shorthand notation
  const result = {
    appId,
    requestId: request._id,
    characters,
    voiceCode,
    audioType,
    progress,
    speedRate: speed,
    bitrate,
    createdAt,
    status,
    audioLink,
    retentionPeriod,
  };

  return result;
};

function modifyVNUrl(originalUrl) {
  const parts = originalUrl.split('/');
  const newUrl = `https://${VN_DOMAIN}/${parts[2].split('.')[0]}/${parts
    .slice(3)
    .join('/')}`;
  return newUrl;
}

const changeDomainUrl = (originalUrl, proxyDomain) => {
  const urlParts = originalUrl.split('/');

  // Find the domain part and replace it with the new one
  urlParts[2] = proxyDomain;

  // Join the URL parts back together
  const newUrl = urlParts.join('/');

  return newUrl;
};

const getAudioName = (title) => {
  const nonAccentVietnameseString = nonAccentVietnamese(title);
  const newTitleRemoveSPecialCharacter = nonAccentVietnameseString.replace(
    REGEX.TEXT_AND_NUMBER,
    '',
  );

  let fileName = newTitleRemoveSPecialCharacter
    .trim()
    .replace(REGEX.SPACE, '_')
    .toLowerCase();

  fileName = `${fileName}_${moment().format('DDMMYYHHmmss')}`;

  return fileName;
};

const updateAudioLink = async (requestId, newTitle) => {
  const request = await requestDao.findRequestById(requestId);
  if (!request) throw new CustomError(code.BAD_REQUEST, 'Request is not found');

  const newAudioName = getAudioName(newTitle);
  const {
    awsZoneFunctions = {},
    awsZoneSynthesis = DEFAULT_AWS_REGION,
    audioType,
  } = request;
  const { s3Bucket = DEFAULT_BUCKET_S3 } = awsZoneFunctions;

  const { audioLink } = request;
  const oldAudioKey = getKeyFromS3Url(audioLink);
  const lastIndex = oldAudioKey.lastIndexOf('/');
  const directoryPath = oldAudioKey.substring(0, lastIndex + 1);

  const newKey = `${directoryPath}${newAudioName}.${audioType}`;

  const newAudioLink = await renameS3File({
    bucketName: s3Bucket,
    oldKey: oldAudioKey,
    newKey,
    awsZoneSynthesis,
  });

  return newAudioLink;
};

const updateRequest = async (requestId, requestInfo) => {
  const request = await requestDao.findRequestById(requestId);
  if (!request) throw new CustomError(code.BAD_REQUEST, 'Request is not found');

  const { title } = requestInfo;
  const { audioLink } = request;
  if (audioLink) {
    const newAudioLink = await updateAudioLink(requestId, title);
    requestInfo.audioLink = newAudioLink;
  }

  const updatedRequest = await requestDao.updateRequestById(requestId, {
    ...requestInfo,
  });

  return updatedRequest;
};

const increaseProcessingRequest = async (key) => {
  const numberProcessingRequest = await redisClient.incr(key);
  return numberProcessingRequest;
};

const decreaseProcessingRequest = async (key) => {
  const numberProcessingRequest = await redisClient.decr(key);

  if (numberProcessingRequest < 0) await setKeyWithTtl(key, 0);

  return numberProcessingRequest;
};

module.exports = {
  createRequest,
  createRequestInRedis,
  updateParagraphsOfRequest,
  saveSynthesisTime,
  getRequestFromSessionId,
  decreaseProcessingRequest,
  modifyVNUrl,
  changeDomainUrl,
  updateRequest,
  increaseProcessingRequest,
};

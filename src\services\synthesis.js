const {
  KAFKA_TOPIC,
  REQUEST_STATUS,
  TTS_CORE_COMPUTE_PLATFORM,
  REDIS_KEY_PREFIX,
  REDIS_KEY_TTL,
  LOADING_SYNTHESIS_FACTOR,
  REQUEST_TYPE,
} = require('../constants');
const code = require('../errors/code');

const {
  handleTtsFailure,
  checkCompletedSynthesis,
  handleUpdateProgressTTS,
  handleTtsSuccess,
  updateProgressWhenSynthesisSuccessInRedis,
  saveTtsIdsInRequest,
  calProgressWhenSynthesisSuccess,
  checkAvailableBreakTime,
} = require('./ttsProcessing');

const { saveSynthesisTime } = require('./request');
const { getVoiceByCode } = require('./voice');
const { sendMessage, sendMessages } = require('./kafka/producer');
const { redisClient } = require('./redis');

const { deleteInProgressRequest } = require('../daos/inProgressRequest');
const {
  findRequestById,
  // updateRequestById,
  updateRequestByIdInRedis,
  findRequestByIdInRedis,
  saveAudioLinkInRedis,
  updateFinalRequestFromRedisToDB,
  checkDoneTts,
} = require('../daos/request');
const {
  saveSentences,
  saveAudioInRedis,
  getAudiosInRedis,
  migrateTtsFromRedisToDB,
  updateFailureTTSInRedis,
  countRealTtsInRedis,
} = require('../daos/tts');
const { splitArray } = require('../utils/object');
// const { handleCacheSentence, getSynthesisSentences } = require('./cache');
const { getMsClientPause } = require('./preprocessing');
const { processTtsDubbingToJoinAudios } = require('./dubbing');
const { getFeatureValue } = require('./growthbook');
const FEATURE_KEYS = require('../constants/featureKeys');
const { getAllTimestamps } = require('./tts');
const callApi = require('../utils/callApi');
const { IS_TTS_GATE } = require('../configs');

const processSentence = ({
  index,
  requestId,
  request,
  ttsCoreVersion,
  voice,
  sentence,
  synthesisFunction,
  voiceOverride = null,
  synthesisComputePlatform = TTS_CORE_COMPUTE_PLATFORM.LAMBDA,
}) => {
  const {
    clientUserId,
    ip,
    type: requestType,
    audioType,
    awsZoneSynthesis,
    awsZoneFunctions,
    clientPause,
    dictionary,
    synthesisCcr,
    returnTimestamp,
    isPriority,
    isRealTime,
    clientUserId: userId,
  } = request;

  const { textToAllophoneFunction, srtFunction, joinSentencesFunction } =
    awsZoneFunctions;
  const { text, subIndex, _id, start, end } = sentence;
  const breakTime = request.sentences?.[index]?.breakTime || 0;

  const voiceValue = voiceOverride || voice;
  let synthesisFunctionValue = synthesisFunction;
  let textValue = text;
  const useBreakTime = checkAvailableBreakTime({
    voice,
    voiceOverride,
    userId,
    ttsCoreVersion,
    request,
    index,
  });

  if (useBreakTime)
    textValue = breakTime === 0 ? text : `${text} <break time=${breakTime}s/>`;
  if (voiceOverride)
    synthesisFunctionValue =
      requestType === REQUEST_TYPE.DUBBING
        ? voiceValue.synthesisFunction
        : awsZoneFunctions.synthesisFunction;

  return {
    userId: clientUserId || ip,
    requestId,
    requestType,
    ttsId: _id,
    index,
    subIndex,
    text: textValue,
    start,
    end,
    voice: voiceValue,
    audioType,
    speed: request.sentences?.[index]?.speed || request.speed,
    sampleRate: request.sampleRate,
    bitrate: request.bitrate,
    dictionary: dictionary || [],
    clientPause: IS_TTS_GATE
      ? clientPause || {}
      : getMsClientPause(clientPause || {}),
    ttsCoreVersion,
    awsZoneSynthesis,
    textToAllophoneFunction,
    synthesisFunction: synthesisFunctionValue,
    srtFunction,
    joinSentencesFunction,
    concurrentRequest: synthesisCcr,
    returnTimestamp,
    synthesisComputePlatform,
    isPriority,
    isRealTime,
  };
};

const downloadSentencesFile = async (url) => {
  const response = await callApi(url);
  return response;
};

const handleSentenceTokenizationSuccessResponse = async ({
  requestId,
  index,
  sentences,
  sentencesUrl,
  voiceCode,
  ttsCoreVersion,
  duration,
}) => {
  const request = await findRequestByIdInRedis(requestId);

  const {
    clientUserId,
    type: requestType,
    audioType,
    sampleRate,
    awsZoneSynthesis,
    awsZoneFunctions,
    sessionId,
    callbackUpdateProgressUrl,
    sentencesVoiceCode,
    synthesisComputePlatform,
    isPriority,
    isRealTime,
    sentences: sentencesFromRequest,
  } = request;

  const isDubbingRequest = requestType === REQUEST_TYPE.DUBBING;
  const isUseDubbingSentencesUrl = getFeatureValue(
    FEATURE_KEYS.USE_DUBBING_SENTENCES_URL,
  );
  if (isDubbingRequest && sentencesUrl && isUseDubbingSentencesUrl)
    sentences = await downloadSentencesFile(sentencesUrl);

  // TODO: refactor
  const { ttsIdRequests: savedSentences } = await saveSentences({
    requestId,
    index,
    type: requestType,
    sentences,
    voiceCode,
    audioType,
    sampleRate,
    awsZoneSynthesis,
    sentencesVoiceCode,
    isPriority,
    isRealTime,
  });

  if (request?.status !== REQUEST_STATUS.IN_PROGRESS) return;
  const voice = await getVoiceByCode(voiceCode);

  await updateRequestByIdInRedis(requestId, {
    sentenceTokenizerDuration: duration,
    text: '', // remove text in cache
    synthesisDuration: {
      start: Date.now(),
    },
  });

  const progress = LOADING_SYNTHESIS_FACTOR.SENTENCE_TOKENIZATION * 100;
  await handleUpdateProgressTTS({
    requestId,
    userId: clientUserId,
    progress,
    status: REQUEST_STATUS.IN_PROGRESS,
    sessionId,
    callbackUpdateProgressUrl,
  });

  // Hide the audio cache feature due to an error of not being able to retrieve the sound, will reopen the feature when the error is fixed
  // const synthesisSentences = await getSynthesisSentences({
  //   sentences: savedSentences,
  //   request,
  //   index,
  //   version: request.version,
  //   awsZoneSynthesis,
  // });
  const synthesisSentences = savedSentences;

  await saveTtsIdsInRequest(requestId);

  if (!synthesisSentences.length) return;
  const synthesisFunction =
    requestType === REQUEST_TYPE.DUBBING
      ? voice.synthesisFunction
      : awsZoneFunctions.synthesisFunction;

  let messages = [];
  if (Object.keys(sentencesVoiceCode || {}).length > 0) {
    messages = await Promise.all(
      synthesisSentences.map(async (sentence) => {
        const sentenceVoiceCode = sentence?.voiceCode || voiceCode;
        const voiceOverride = await getVoiceByCode(sentenceVoiceCode);
        const value = processSentence({
          index,
          requestId,
          request,
          ttsCoreVersion,
          synthesisComputePlatform:
            sentencesFromRequest?.[index]?.synthesisComputePlatform ||
            synthesisComputePlatform ||
            TTS_CORE_COMPUTE_PLATFORM.LAMBDA,
          voice,
          sentence,
          synthesisFunction,
          voiceOverride,
        });

        return { value };
      }),
    );
  } else {
    messages = synthesisSentences.reduce((acc, sentence) => {
      const value = processSentence({
        index,
        requestId,
        request,
        ttsCoreVersion,
        synthesisComputePlatform:
          sentencesFromRequest?.[index]?.synthesisComputePlatform ||
          synthesisComputePlatform ||
          TTS_CORE_COMPUTE_PLATFORM.LAMBDA,
        voice,
        sentence,
        synthesisFunction,
      });

      return [...acc, { value }];
    }, []);
  }

  // Fix error: The request included a message larger than the max message size the server will accept
  const chunkSize = 100;
  const messageToSend = splitArray(messages, chunkSize);

  messageToSend.map((message) =>
    sendMessages(KAFKA_TOPIC.SYNTHESIS_REQUEST, message),
  );
};

const handleSentenceTokenizationFailureResponse = async ({
  requestId,
  errorCode,
  error,
}) => {
  const request = await findRequestByIdInRedis(requestId);
  if (request?.status !== REQUEST_STATUS.IN_PROGRESS) return;

  const finalRequest = await updateRequestByIdInRedis(requestId, {
    status: REQUEST_STATUS.FAILURE,
    error,
    errorCode,
  });
  await deleteInProgressRequest(requestId);

  await updateFinalRequestFromRedisToDB(requestId, finalRequest);

  handleTtsFailure({
    request: finalRequest,
    errorCode: code.TTS_FAILURE,
    errorMessage: error,
  });

  const { clientUserId, sessionId, callbackUpdateProgressUrl } = finalRequest;
  handleUpdateProgressTTS({
    requestId,
    userId: clientUserId,
    status: REQUEST_STATUS.FAILURE,
    sessionId,
    callbackUpdateProgressUrl,
    error,
  });
};

const handleSynthesisSuccessResponse = async ({
  requestId,
  index,
  // text,
  ttsId,
  subIndex,
  audioName,
  audioLink,
  t2aDuration,
  synthesisDuration,
  phrases,
  timestampWords,
  // cache,
}) => {
  await saveAudioInRedis({
    requestId,
    index,
    subIndex,
    ttsId,
    audioName,
    audioLink,
    t2aDuration,
    synthesisDuration,
    timestampWords,
  });

  // TODO: set redis and time to live
  const request = await findRequestByIdInRedis(requestId);
  const statusRequest = await redisClient.get(`REQUEST_STATUS_${requestId}`);
  // value of updateStatusRequest > 0 means that request status was done
  if (
    statusRequest ||
    statusRequest > 0 ||
    request?.status === REQUEST_STATUS.FAILURE
  ) {
    const isDoneRequest = await checkDoneTts(requestId);
    if (isDoneRequest) await migrateTtsFromRedisToDB(requestId);
    return;
  }

  if (request?.status !== REQUEST_STATUS.IN_PROGRESS) return;

  // Hide the audio cache feature due to an error of not being able to retrieve the sound, will reopen the feature when the error is fixed
  // Check data from cache or synthesis service
  // if (
  //   !cache &&
  //   phrases?.length > 0 &&
  //   request.version === TTS_CORE_VERSION.NEW
  // ) {
  //   const {
  //     userId,
  //     voice,
  //     audioType,
  //     speed,
  //     bitrate,
  //     sampleRate,
  //     awsZoneSynthesis,
  //   } = request;
  //   handleCacheSentence({
  //     userId,
  //     text,
  //     phrases,
  //     voiceCode: voice.code,
  //     audioType,
  //     speed,
  //     bitrate,
  //     sampleRate,
  //     awsZoneSynthesis,
  //   });
  // }

  const completedTtsKey = `${REDIS_KEY_PREFIX.COMPLETED_SYNTHESIS}_${requestId}`;
  const numOfCompletedTts = await redisClient.incr(completedTtsKey);
  redisClient.expire(completedTtsKey, REDIS_KEY_TTL.COMPLETED_SYNTHESIS);

  const numOfTts = await countRealTtsInRedis(requestId);

  const { clientUserId, sessionId, callbackUpdateProgressUrl } = request;

  await updateProgressWhenSynthesisSuccessInRedis({
    requestId,
    userId: clientUserId,
    status: REQUEST_STATUS.IN_PROGRESS,
    sessionId,
    audioLink,
    index,
    subIndex,
    phrases,
    progress: calProgressWhenSynthesisSuccess(numOfCompletedTts, numOfTts),
    callbackUpdateProgressUrl,
  });

  const isCompleted = await checkCompletedSynthesis({
    requestId,
    numberOfSentences: request.numberOfSentences || 1,
    numOfCompletedTts,
    numOfTts,
  });

  if (isCompleted) {
    const requestStatusKey = `${REDIS_KEY_PREFIX.REQUEST_STATUS}_${requestId}`;
    await redisClient.incr(requestStatusKey);
    redisClient.expire(requestStatusKey, REDIS_KEY_TTL.REQUEST_STATUS);

    const {
      awsZoneSynthesis,
      awsZoneFunctions = {},
      sampleRate,
      bitrate,
      audioType,
    } = request;
    const { joinSentencesFunction, s3Bucket, srtFunction } = awsZoneFunctions;

    const audios = await getAudiosInRedis(requestId);
    let message;
    if (request.type === REQUEST_TYPE.DUBBING) {
      const subtitles = await processTtsDubbingToJoinAudios(requestId);

      message = {
        value: {
          requestId,
          subtitles,
          sampleRate,
          bitrate,
          audioType,
          title: request.title,
          awsZoneSynthesis,
          srtFunction,
          s3Bucket,
        },
      };
    } else {
      message = {
        value: {
          requestId,
          audios,
          title: request.title,
          backgroundMusic: request.backgroundMusic,
          audioType: request.audioType,
          sampleRate,
          bitrate,
          volume: request.volume,
          awsZoneSynthesis,
          joinSentencesFunction,
          s3Bucket,
        },
      };
    }
    sendMessage(KAFKA_TOPIC.JOIN_AUDIOS_REQUEST, message);
    saveSynthesisTime(requestId, request?.synthesisDuration?.start);
  }
};

const handleSynthesisFailureResponse = async ({
  requestId,
  ttsId,
  error,
  errorCode,
}) => {
  await updateFailureTTSInRedis({ requestId, ttsId, error, errorCode });

  const statusRequest = await redisClient.get(
    `${REDIS_KEY_PREFIX.REQUEST_STATUS}_${requestId}`,
  );
  const request = await findRequestByIdInRedis(requestId);
  // value of updateStatusRequest > 0 means that request status was is done
  if (statusRequest) {
    const isDoneRequest = await checkDoneTts(requestId);
    if (isDoneRequest) await migrateTtsFromRedisToDB(requestId);
    return;
  }

  const requestStatusKey = `${REDIS_KEY_PREFIX.REQUEST_STATUS}_${requestId}`;
  await redisClient.incr(requestStatusKey);
  redisClient.expire(requestStatusKey, REDIS_KEY_TTL.REQUEST_STATUS);
  if (request?.status !== REQUEST_STATUS.IN_PROGRESS) return;

  const finalRequest = await updateRequestByIdInRedis(requestId, {
    status: REQUEST_STATUS.FAILURE,
    error,
    errorCode,
  });

  deleteInProgressRequest(requestId);

  handleTtsFailure({
    request: finalRequest,
    errorCode: code.TTS_FAILURE,
    errorMessage: error,
  });

  const { clientUserId, sessionId, callbackUpdateProgressUrl } = finalRequest;
  handleUpdateProgressTTS({
    requestId,
    userId: clientUserId,
    status: REQUEST_STATUS.FAILURE,
    sessionId,
    callbackUpdateProgressUrl,
    error,
  });

  await saveSynthesisTime(requestId, request?.synthesisDuration?.start);
  updateFinalRequestFromRedisToDB(requestId, finalRequest);
};

const handleJoinAudiosSuccessResponse = async ({
  requestId,
  audioLink,
  duration,
}) => {
  const request = await findRequestById(requestId);
  if (request?.status !== REQUEST_STATUS.IN_PROGRESS) return;

  await updateRequestByIdInRedis(requestId, {
    progress: 100,
    joinAudioDuration: duration,
  });
  await deleteInProgressRequest(requestId);

  let timestampArr = [];
  const useReturnTimestamp = getFeatureValue(FEATURE_KEYS.TIMESTAMP_WORDS, {
    userId: request.clientUserId,
  });
  if (request.returnTimestamp && useReturnTimestamp)
    timestampArr = await getAllTimestamps(requestId);

  const finalRequest = await saveAudioLinkInRedis({
    requestId,
    audioLink,
    timestampArr,
  });
  await updateFinalRequestFromRedisToDB(requestId, finalRequest);

  handleTtsSuccess(finalRequest);
};

const handleJoinAudiosFailureResponse = async ({
  requestId,
  error,
  errorCode,
}) => {
  const request = await findRequestByIdInRedis(requestId);
  if (request?.status !== REQUEST_STATUS.IN_PROGRESS) return;

  const endedAt = new Date();
  const finalRequest = await updateRequestByIdInRedis(requestId, {
    status: REQUEST_STATUS.FAILURE,
    error,
    endedAt,
    errorCode,
  });

  updateFinalRequestFromRedisToDB(requestId, finalRequest);
  deleteInProgressRequest(requestId);
  migrateTtsFromRedisToDB(requestId);

  handleTtsFailure({
    request: finalRequest,
    errorCode: code.TTS_FAILURE,
    errorMessage: error,
  });
  const { clientUserId, sessionId, callbackUpdateProgressUrl } = finalRequest;
  handleUpdateProgressTTS({
    requestId,
    userId: clientUserId,
    status: REQUEST_STATUS.FAILURE,
    sessionId,
    callbackUpdateProgressUrl,
    error,
  });
};

module.exports = {
  handleSentenceTokenizationSuccessResponse,
  handleSentenceTokenizationFailureResponse,
  handleSynthesisSuccessResponse,
  handleSynthesisFailureResponse,
  handleJoinAudiosSuccessResponse,
  handleJoinAudiosFailureResponse,
};

const router = require('express').Router();
const asyncMiddleware = require('../middlewares/async');
const synthesisApiController = require('../controllers/api');
const cachingRequestController = require('../controllers/caching');
const { authAPI } = require('../middlewares/auth');
const {
  cachingRequestValidate,
  countTextLengthValidate,
} = require('../validations/caching');
const { synthesisApiValidate } = require('../validations/api');

/* eslint-disable prettier/prettier */
router.post('/tts', authAPI, synthesisApiValidate, asyncMiddleware(synthesisApiController.apiSynthesis));
router.post('/tts/mobifone', authAPI, asyncMiddleware(synthesisApiController.apiSynthesisMobifone));
router.post('/tts/caching', authAPI, cachingRequestValidate, asyncMiddleware(cachingRequestController.cachingSynthesis));
router.post('/tts/caching/text-length', authAPI, countTextLengthValidate, asyncMiddleware(cachingRequestController.countTextLength));
/* eslint-disable prettier/prettier */

module.exports = router;

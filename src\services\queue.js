const { REDIS_KEY_PREFIX, KAFKA_TOPIC, REQUEST_TYPE } = require('../constants');
const requestContentDao = require('../daos/requestContent');
const { processText, getMsClientPause } = require('./preprocessing');
const { sendMessage, sendMessages } = require('./kafka/producer');
const { createRequestInRedis } = require('./request');
const { parseSRT, formatSubtitle } = require('../utils/srt');
const { SUBTITLE_PLACEHOLDER } = require('../constants/dubbing');

const replaceEmptyContent = (subtitle) => {
  const { content } = subtitle;
  return {
    ...subtitle,
    content: content || SUBTITLE_PLACEHOLDER,
  };
};

const getDubbingText = (text, isDubbingMultiVoice) => {
  if (!isDubbingMultiVoice) return text;

  const { parsedSubtitles } = parseSRT(text);

  // Replace the empty content with subtitle placeholders cause sentence tokenizer just return the text has content.
  // That make voice synthesis can't get the correct index of subtitle.
  const newSubtitles = parsedSubtitles.map((subtitle) =>
    replaceEmptyContent(subtitle),
  );

  // Convert subtitle objects back to SRT format
  const formattedSubtitles = newSubtitles.map(formatSubtitle);
  return formattedSubtitles.join('\n');
};

const getText = async ({
  requestType,
  text,
  voice,
  version,
  paragraphBreak,
  hasClientPause,
  isDubbingMultiVoice = false,
}) => {
  if (requestType === REQUEST_TYPE.DUBBING) {
    const dubbingText = getDubbingText(text, isDubbingMultiVoice);
    return dubbingText;
  }

  const normalizeText = await processText({
    text,
    voiceProvider: voice.provider,
    voiceLanguage: voice.languageCode,
    ttsCoreVersion: version,
    paragraphBreak,
    hasClientPause,
  });

  return normalizeText;
};

const sendPendingRequestToKafka = async ({
  request = {},
  userId,
  textAfterHandleAcronym = '',
}) => {
  const { requestId } = request;

  try {
    const requestContent = await requestContentDao.getRequestContent(requestId);
    if (request.type === REQUEST_TYPE.API_CACHING) {
      request.sentenceKeys = [`${REDIS_KEY_PREFIX.SENTENCE}_${requestId}_${0}`];
      await createRequestInRedis(request);
    }
    if (!request) throw new Error('Request is not exists');
    const { clientPause } = request;

    const hasClientPause = !!clientPause;
    const { paragraphBreak, sentenceBreak, majorBreak, mediumBreak } =
      clientPause || {};

    const kafkaTopic = KAFKA_TOPIC.SENTENCE_TOKENIZATION_REQUEST;

    const {
      numberOfSentences,
      voice = {},
      awsZoneFunctions,
      awsZoneSynthesis,
      type,
      version,
      clientUserId,
      sentencesVoiceCode,
    } = request;
    const { text } = requestContent;
    const {
      sentenceTokenizerFunction,
      newSentenceTokenizerFunction,
      srtFunction,
    } = awsZoneFunctions;

    const voiceCodes = Object.keys(sentencesVoiceCode || {});
    const isDubbingMultiVoice = voiceCodes.length > 0;

    // multi voice
    if (numberOfSentences > 0) {
      const sentences =
        await requestContentDao.getDetailSentencesByRequestContentId(requestId);

      const messages = await Promise.all(
        sentences.map(async (sentence, index) => {
          const { text: sentenceText, voice: sentenceVoice } = sentence;

          const normalizeText = await processText({
            text: sentenceText,
            voiceProvider: sentenceVoice.provider,
            voiceLanguage: sentenceVoice.languageCode,
            ttsCoreVersion: request.version,
            paragraphBreak,
            hasClientPause,
          });

          return {
            value: {
              requestId,
              index,
              text: normalizeText,
              voice: sentenceVoice,
              ttsCoreVersion: request.version,
              awsZoneSynthesis,
              sentenceTokenizerFunction,
              newSentenceTokenizerFunction,
              clientUserId,
              clientPause: getMsClientPause({
                paragraphBreak,
                sentenceBreak,
                majorBreak,
                mediumBreak,
              }),
            },
          };
        }),
      );

      await sendMessages(kafkaTopic, messages);
      return;
    }

    const normalizeText = await getText({
      text: textAfterHandleAcronym || text,
      requestType: type,
      voice,
      version,
      paragraphBreak,
      hasClientPause,
      isDubbingMultiVoice,
    });

    const data = {
      requestId,
      index: 0,
      text: normalizeText,
      voice,
      ttsCoreVersion: request.version,
      clientPause: getMsClientPause({
        paragraphBreak,
        sentenceBreak,
        majorBreak,
        mediumBreak,
      }),
      awsZoneSynthesis,
      sentenceTokenizerFunction,
      newSentenceTokenizerFunction,
      clientUserId,
      srtFunction,
    };

    await sendMessage(kafkaTopic, { value: data });
  } catch (error) {
    logger.error(error, { ctx: 'ProcessRequestByCcr', requestId, userId });
    require('./synthesis').handleSentenceTokenizationFailureResponse(
      requestId,
      error.message,
    );
  }
};

module.exports = {
  sendPendingRequestToKafka,
};

const voiceService = require('../services/voice');

const createVoice = async (req, res) => {
  const {
    code,
    name,
    image,
    gender,
    status,
    type,
    languageCode,
    provider,
    squareImage,
    roundImage,
    sampleRates,
    defaultSampleRate,
    global,
    synthesisFunction,
  } = req.body;

  const voice = await voiceService.createVoice({
    code,
    name,
    image,
    gender,
    status,
    type,
    languageCode,
    provider,
    squareImage,
    roundImage,
    sampleRates,
    defaultSampleRate,
    global,
    synthesisFunction,
  });

  return res.send({ voice });
};

module.exports = { createVoice };

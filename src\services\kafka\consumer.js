/* eslint-disable array-callback-return */
require('dotenv').config();
const camelcaseKeys = require('camelcase-keys');

const kafka = require('./kafka');
const {
  KAFKA_CONSUMER_GROUP_TTS,
  LOG_SHORTEN,
  SYNTHESIS_TTS_API,
} = require('../../configs');
const { KAFKA_TOPIC } = require('../../constants');
const {
  handleCachingSynthesisSuccess,
  handleCachingSynthesisFailure,
} = require('../cachingSynthesis');
const { sendApiResponse } = require('../apiResponse');
const {
  handleSentenceTokenizationSuccessResponse,
  handleSentenceTokenizationFailureResponse,
  handleSynthesisSuccessResponse,
  handleJoinAudiosSuccessResponse,
  handleJoinAudiosFailureResponse,
  handleSynthesisFailureResponse,
} = require('../synthesis');

const consumer = kafka.consumer({ groupId: KAFKA_CONSUMER_GROUP_TTS });
const publishConsumer = kafka.consumer({
  groupId: `${KAFKA_CONSUMER_GROUP_TTS}-${KAFKA_CONSUMER_GROUP_RANDOM_TTS}`,
});

const consume = async () => {
  await consumer.connect();
  const apiTopic = [
    KAFKA_TOPIC.SENTENCE_TOKENIZATION_SUCCESS,
    KAFKA_TOPIC.SENTENCE_TOKENIZATION_FAILURE,
    KAFKA_TOPIC.SYNTHESIS_SUCCESS,
    KAFKA_TOPIC.SYNTHESIS_FAILURE,
    KAFKA_TOPIC.JOIN_AUDIOS_SUCCESS,
    KAFKA_TOPIC.JOIN_AUDIOS_FAILURE,
  ];

  let topics = [
    KAFKA_TOPIC.CACHING_SYNTHESIS_SUCCESS,
    KAFKA_TOPIC.CACHING_SYNTHESIS_FAILURE,
  ];
  if (SYNTHESIS_TTS_API) topics = [...topics, ...apiTopic];
  topics.forEach((topic) => consumer.subscribe({ topic }));

  await consumer.run({
    eachMessage: async ({ topic, partition, message }) => {
      try {
        logger.info(
          LOG_SHORTEN
            ? { topic }
            : {
                topic,
                partition,
                offset: message.offset,
                timestamp: message.timestamp,
                key: message.key ? message.key.toString() : null,
                value: message.value.toString(),
              },
          { ctx: 'KafkaConsume' },
        );

        const data = camelcaseKeys(JSON.parse(message.value.toString()), {
          deep: true,
        });

        switch (topic) {
          case KAFKA_TOPIC.SENTENCE_TOKENIZATION_SUCCESS: {
            const {
              requestId,
              index,
              voice,
              sentencesUrl,
              sentences,
              ttsCoreVersion,
              duration,
            } = data;
            handleSentenceTokenizationSuccessResponse({
              requestId,
              index,
              sentences,
              sentencesUrl,
              voiceCode: voice.code,
              ttsCoreVersion,
              duration,
            });
            break;
          }

          case KAFKA_TOPIC.SENTENCE_TOKENIZATION_FAILURE: {
            const { requestId, errorCode, errorMessage } = data;
            handleSentenceTokenizationFailureResponse({
              requestId,
              errorCode,
              error: errorMessage,
            });
            break;
          }

          case KAFKA_TOPIC.SYNTHESIS_SUCCESS: {
            const {
              requestId,
              index,
              subIndex,
              ttsId,
              text,
              audioName,
              audioLink,
              phrases,
              t2ADuration: t2aDuration,
              synthesisDuration,
              cache,
              timestampWords,
            } = data;

            handleSynthesisSuccessResponse({
              requestId,
              index,
              text,
              subIndex,
              ttsId,
              audioName,
              audioLink,
              t2aDuration,
              synthesisDuration,
              phrases,
              cache,
              timestampWords,
            });
            break;
          }

          case KAFKA_TOPIC.SYNTHESIS_FAILURE: {
            const {
              requestId,
              index,
              subIndex,
              ttsId,
              errorCode,
              errorMessage,
            } = data;
            handleSynthesisFailureResponse({
              requestId,
              index,
              ttsId,
              subIndex,
              error: errorMessage,
              errorCode,
            });
            break;
          }

          case KAFKA_TOPIC.JOIN_AUDIOS_SUCCESS: {
            const { requestId, audioLink, duration } = data;
            handleJoinAudiosSuccessResponse({
              requestId,
              audioLink,
              duration,
            });
            break;
          }

          case KAFKA_TOPIC.JOIN_AUDIOS_FAILURE: {
            const { requestId, errorCode, errorMessage } = data;
            handleJoinAudiosFailureResponse({
              requestId,
              error: errorMessage,
              errorCode,
            });
            break;
          }

          case KAFKA_TOPIC.CACHING_SYNTHESIS_SUCCESS: {
            const {
              requestId,
              statusCode,
              audioLink,
              totalTime,
              message: cachingMessage,
              getSynthesisRequestAt,
              startInvokeLambdaFunctionAt,
              endInvokeLambdaFunctionAt,
              isFromCache,
            } = data;

            handleCachingSynthesisSuccess({
              requestId,
              statusCode,
              audioLink,
              totalTime,
              cachingMessage,
              getSynthesisRequestAt,
              startInvokeLambdaFunctionAt,
              endInvokeLambdaFunctionAt,
              isFromCache,
            });
            break;
          }

          case KAFKA_TOPIC.CACHING_SYNTHESIS_FAILURE: {
            const {
              requestId,
              statusCode,
              message: cachingMessage,
              totalTime: cachingTime,
            } = data;

            handleCachingSynthesisFailure({
              requestId,
              statusCode,
              cachingMessage,
              cachingTime,
            });
            break;
          }

          default:
            break;
        }
      } catch (error) {
        logger.error(error, { ctx: 'KafkaConsume' });
      }
    },
  });
};

const publishConsume = async () => {
  await publishConsumer.connect();

  const topics = [
    KAFKA_TOPIC.PUBLISH_TTS_SUCCESS,
    KAFKA_TOPIC.PUBLISH_TTS_FAILURE,
  ];
  topics.forEach((topic) => publishConsumer.subscribe({ topic }));

  await publishConsumer.run({
    eachMessage: async ({ topic, partition, message }) => {
      try {
        logger.info(
          LOG_SHORTEN
            ? { topic }
            : {
                topic,
                partition,
                offset: message.offset,
                timestamp: message.timestamp,
                key: message.key ? message.key.toString() : null,
                value: message.value.toString(),
              },
          { ctx: 'KafkaConsume' },
        );

        const data = camelcaseKeys(JSON.parse(message.value.toString()), {
          deep: true,
        });

        switch (topic) {
          case KAFKA_TOPIC.PUBLISH_TTS_SUCCESS: {
            const { requestId } = data;

            const res = global.REQUEST_DIRECT[requestId];
            logger.info(
              { haveRes: !!res, requestId },
              { ctx: 'HandlePublishTtsSuccess' },
            );
            if (!res) return;
            sendApiResponse({ request: data, res });
            break;
          }

          case KAFKA_TOPIC.PUBLISH_TTS_FAILURE: {
            const { requestId, errorCode, errorMessage } = data;

            const res = global.REQUEST_DIRECT[requestId];
            if (!res) return;
            sendApiResponse({ request: data, res, errorMessage, errorCode });
            break;
          }

          default:
            break;
        }
      } catch (error) {
        logger.error(error, { ctx: 'KafkaConsume' });
      }
    },
  });
};

consume().catch((e) => {
  logger.error(e, { ctx: 'KafkaConsume' });
});
publishConsume().catch((e) => {
  logger.error(e, { ctx: 'KafkaConsume' });
});

const errorTypes = ['unhandledRejection', 'uncaughtException'];
const signalTraps = ['SIGTERM', 'SIGINT', 'SIGUSR2'];

errorTypes.map((type) => {
  process.on(type, async (e) => {
    try {
      logger.info(`process.on ${type}`);
      logger.error(e);
      await consumer.disconnect();
      process.exit(0);
    } catch (_) {
      process.exit(1);
    }
  });
});

signalTraps.map((type) => {
  process.once(type, async () => {
    try {
      await consumer.disconnect();
    } finally {
      process.kill(process.pid, type);
    }
  });
});

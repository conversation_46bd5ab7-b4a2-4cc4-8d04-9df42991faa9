const retry = require('retry');
const callApi = require('../utils/callApi');
const {
  TTS_CORE_URL,
  TTS_CORE_APP_ID,
  TTS_CORE_ACCESS_TOKEN,
  TTS_GATE_API_URL,
} = require('../configs');

const callApiSynthesizeSubtile = async ({
  userId,
  requestId,
  title,
  audioType,
  bitrate,
  sampleRate,
  speedRate,
  retentionPeriod,
  synthesisCcr,
  subtitleLink,
  sentencesVoiceCode,
  voiceCode,
}) => {
  const data = {
    title,
    appId: TTS_CORE_APP_ID,
    callbackUrl: `${TTS_GATE_API_URL}/api/v1/tts-gate-api/callback`,
    responseType: 'indirect',
    audioType,
    bitrate,
    sampleRate,
    voiceCode,
    retentionPeriod,
    clientUserId: userId,
    sessionId: requestId,
    speedRate,
    synthesisCcr,
    subtitleLink,
    sentencesVoiceCode,
  };

  const res = await callApi({
    headers: { authorization: `Bearer ${TTS_CORE_ACCESS_TOKEN}` },
    url: `${TTS_CORE_URL}/api/v1/dubbing`,
    method: 'POST',
    data,
  });
  return res;
};

const executeCallApiSynthesizeSubtitle = async (data) =>
  new Promise((resolve) => {
    const { requestId } = data;
    const operation = retry.operation({
      retries: 3, // Maximum 3 retries
      factor: 2, // Exponential backoff factor
      minTimeout: 2000, // Start with 2 second delay
      maxTimeout: 30000, // Max 30 seconds between retries
      randomize: true, // Add some randomization to prevent thundering herd
    });

    operation.attempt(async (currentAttempt) => {
      try {
        const result = await callApiSynthesizeSubtile(data);
        resolve(result);
      } catch (err) {
        if (operation.retry(err)) {
          logger.warn(
            `Call API TTS Core synthesis attempt ${currentAttempt} failed, retrying...`,
            { ctx: 'ExecuteCallApiTtsCore', requestId },
          );
          return;
        }
        const error = operation.mainError();
        logger.error(error, {
          ctx: 'ExecuteCallApiSynthesizeSubtitle',
          requestId: data.requestId,
        });

        const errorMessage =
          JSON.stringify(error?.response?.data) || error.message;
        require('./synthesis').handleJoinAudiosFailureResponse({
          requestId: data.requestId,
          error: errorMessage,
          errorCode: error.code,
        });
        resolve(null);
      }
    });
  });

const callApiCreateVoice = async (data) => {
  const res = await callApi({
    headers: { authorization: `Bearer ${TTS_CORE_ACCESS_TOKEN}` },
    url: `${TTS_CORE_URL}/api/v1/voices`,
    method: 'POST',
    data,
  });

  return res;
};

const executeCallApiCreateVoice = async (data) =>
  new Promise((resolve, reject) => {
    const { requestId } = data;
    const operation = retry.operation({
      retries: 3, // Maximum 3 retries
      factor: 2, // Exponential backoff factor
      minTimeout: 2000, // Start with 2 second delay
      maxTimeout: 30000, // Max 30 seconds between retries
      randomize: true, // Add some randomization to prevent thundering herd
    });

    operation.attempt(async (currentAttempt) => {
      try {
        const result = await callApiCreateVoice(data);
        resolve(result);
      } catch (err) {
        if (operation.retry(err)) {
          logger.warn(
            `Call API TTS Core synthesis attempt ${currentAttempt} failed, retrying...`,
            { ctx: 'ExecuteCallApiCreateVoice', requestId },
          );
          return;
        }
        const error = operation.mainError();
        logger.error(error, {
          ctx: 'ExecuteCallApiCreateVoice',
          requestId: data.requestId,
        });

        reject(error);
      }
    });
  });

module.exports = {
  executeCallApiSynthesizeSubtitle,
  executeCallApiCreateVoice,
};

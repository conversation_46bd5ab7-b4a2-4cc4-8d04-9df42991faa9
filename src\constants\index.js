const KAFKA_TOPIC = {
  SENTENCE_TOKENIZATION_REQUEST: 'sentence-tokenization-request',
  SENTENCE_TOKENIZATION_SUCCESS: 'sentence-tokenization-success',
  SENTENCE_TOKENIZATION_FAILURE: 'sentence-tokenization-failure',

  SY<PERSON>HESIS_REQUEST: 'synthesis-request',
  SYNTHESIS_SUCCESS: 'synthesis-success',
  SYNTHESIS_FAILURE: 'synthesis-failure',

  CACHING_SYNTHESIS_REQUEST: 'caching-synthesis-request',
  CACHING_SYNTHESIS_SUCCESS: 'caching-synthesis-success',
  CACHING_SYNTHESIS_FAILURE: 'caching-synthesis-failure',

  JOIN_AUDIOS_REQUEST: 'join-audios-request',
  JOIN_AUDIOS_SUCCESS: 'join-audios-success',
  JOIN_AUDIOS_FAILURE: 'join-audios-failure',

  PUBLISH_TTS_SUCCESS: 'publish-tts-success',
  PUBLISH_TTS_FAILURE: 'publish-tts-failure',
};

const REQUEST_TYPE = {
  API_CACHING: 'API_CACHING',
  API: 'API',
  DUBBING: 'DUBBING',
};

const VOICE_LEVEL = {
  BASIC: 'BASIC',
  ADVANCED: 'ADVANCED',
  PRO: 'PRO',
  STANDARD: 'STANDARD',
  PREMIUM: 'PREMIUM',
};

const VOICE_PROVIDER = {
  GOOGLE: 'google',
  AMAZON: 'amazon',
  VBEE: 'vbee',
  VBEE_VOICE_CLONING: 'vbee-voice-cloning',
  MICROSOFT: 'microsoft',
};

const NON_VBEE_PROVIDERS = [
  VOICE_PROVIDER.GOOGLE,
  VOICE_PROVIDER.AMAZON,
  VOICE_PROVIDER.MICROSOFT,
];

const AUDIO_TYPE = {
  MP3: 'mp3',
  WAV: 'wav',
};

const REQUEST_STATUS = {
  IN_PROGRESS: 'IN_PROGRESS',
  SUCCESS: 'SUCCESS',
  FAILURE: 'FAILURE',
};

const TTS_STATUS = {
  IN_PROGRESS: 'IN_PROGRESS',
  SUCCESS: 'SUCCESS',
  FAILURE: 'FAILURE',
};

const EMPHASIS_LEVEL = {
  STRONG: 'strong',
  MODERATE: 'moderate',
  REDUCED: 'reduced',
};

const RESPONSE_TYPE = {
  DIRECT: 'direct',
  INDIRECT: 'indirect',
};

const TTS_CORE_VERSION = {
  OLD: '1.0',
  NEW: '1.1',
};

const TTS_CORE_COMPUTE_PLATFORM = {
  LAMBDA: 'LAMBDA',
  GPU: 'GPU',
  CLOUD_RUN: 'CLOUD_RUN',
};

const OUTPUT_TYPE = {
  LINK: 'link',
  BINARY: 'binary',
};

const VALID_SAMPLE_RATE = [8000, 16000, 22050, 32000, 44100, 48000];

const VALID_BIT_RATE = [8, 16, 32, 64, 128, 160];

const VALID_SPEED = {
  MIN: 0.25,
  MAX: 1.9,
};

const VALID_BACKGROUND_MUSIC_VOLUME = {
  MIN: 0,
  MAX: 100,
  DEFAULT: 80,
};

const LOADING_SYNTHESIS_FACTOR = {
  SENTENCE_TOKENIZATION: 0.1,
  SYNTHESIS_SENTENCE_SUCCESS: 0.8,
  JOIN_AUDIO: 0.1,
};

const VIETNAMESE_LETTERS =
  'àáâãèéêìíòóôõùúăđĩũơưăạảấầẩẫậắằẳẵặẹẻẽềềểếễệỉịọỏốồổỗộớờởỡợụủứừửữựỳỵỷỹ';

const VALID_CHARACTERS_LENGTH_REGEX = new RegExp(
  `([a-zA-Z${VIETNAMESE_LETTERS}]|[0-9]){30,}`,
  'gi',
);

const REDIS_KEY_PREFIX = {
  STUDIO_PENDING_REQUESTS: 'STUDIO_PENDING_REQUESTS', // pending request ids of user studio
  API_PENDING_REQUESTS: 'API_PENDING_REQUESTS', // pending request ids of user api
  STUDIO_PENDING_AND_INPROGRESS_REQUESTS:
    'STUDIO_PENDING_AND_INPROGRESS_REQUESTS', // number of pending and inprogress requests of user studio
  API_PENDING_AND_INPROGRESS_REQUESTS: 'API_PENDING_AND_INPROGRESS_REQUESTS', // number of pending and inprogress requests of user api
  FAILURE_REQUEST: 'FAILURE_REQUEST', // number of failure of request
  SUCCESS_REQUEST: 'SUCCESS_REQUEST', // number of success of request
  COMPLETED_SYNTHESIS: 'COMPLETED_SYNTHESIS', // number of completed tts synthesis
  COMPLETED_SENTENCE_TOKENIZATION: 'COMPLETED_SENTENCE_TOKENIZATION', // number of completed sentence tokenization
  REQUEST: 'REQUEST', // prefix of cache request
  TTS: 'TTS', // prefix of cache tts
  SENTENCE: 'SENTENCE', // prefix of cache sentence
  REQUEST_STATUS: 'REQUEST_STATUS', // prefix of cache request status
  SENTENCE_TOKENIZATION_STATUS: 'SENTENCE_TOKENIZATION_STATUS', // prefix of cache sentence tokenization status
  MIGRATED_TTS: 'MIGRATED_TTS', // prefix of check migrated tts
  APP: 'APP', // prefix of app
  TOTAL_TTS: 'TOTAL_TTS', // total tts counter
};

const REDIS_KEY_TTL = {
  FAILURE_REQUEST: 60 * 60 * 3, // 3 hours
  SUCCESS_REQUEST: 60 * 60 * 3, // 3 hours
  COMPLETED_SYNTHESIS: 60 * 60 * 3, // 3 hours
  COMPLETED_SENTENCE_TOKENIZATION: 60 * 60 * 3, // 3 hours
  SYNTHESIS_REQUEST: 60 * 60 * 3, // 3 hours (for cache synthesis request)
  SYNTHESIS_TTS: 60 * 60 * 3, // 3 hours (for cache synthesis tts)
  REQUEST_STATUS: 60 * 60 * 3, // 3 hours (for cache request status)
  SENTENCE: 60 * 60 * 3, // 3 hours (for cache sentence)
  SENTENCE_TOKENIZATION_STATUS: 60 * 60 * 3, // 3 hours (for cache sentence tokenizer status)
  MIGRATED_TTS: 60 * 60 * 3, // 3 hours (for cache sentence tokenizer status)
  CACHE_AUDIO: 60 * 60 * 24 * 1, // cache sentence audio 1 day for tts
  CACHE_AUDIO_TTS_CACHING: 60 * 60 * 24 * 4, // cache sentence audio 4 days for tts caching,
  TTS_PROCESSING_TIME: 60 * 60 * 3, // 3 hours
  DEFAULT: 60 * 60 * 3, // 3 hours
};

const REGEX = {
  BREAK_LINE: /((\s*)(\n)(\s*))+/g,
  VBEE_SPEED: /(?:(\+|-)[0-9.]+%)/g,
  OLD_BREAK_TIME: /[<]break\s+time[=]([0-9.]+)[s][/][>]/g,
  NEW_BREAK_TIME: /[<]break\s+time[=]["]([0-9.]+)[s]["][/][>]/g,
  ADVANCE_TAG:
    /[<]break\s+time[=]["]([0-9.]+)[s]["][/][>]|[<]emphasis\s+level[=]["](?:strong|moderate|reduced)["][>]|[<][/]emphasis[>]|[<]prosody\s+rate[=]["](?:x-fast|fast|medium|slow|x-slow|(?:(\+|-)[0-9.]+%))["][>]|[<][/]prosody[>]/g,
  CACHING_PERSONAL_TAG: /{.+?}/g,
  ACRONYM: /([\p{L}\s,.\-–&]+?)\s*\(([\p{L}\s&\-–.]+)\)/gu,
  AND_CHARACTER: /và/g,
  // This below is merged from OLD_BREAK_TIME and ADVANCE_TAG
  MULTIPLE_TAGS:
    /[<]break\s+time[=]([0-9.]+)[s][/][>]|[<]break\s+time[=]["]([0-9.]+)[s]["][/][>]|[<]emphasis\s+level[=]["](?:strong|moderate|reduced)["][>]|[<][/]emphasis[>]|[<]prosody\s+rate[=]["](?:x-fast|fast|medium|slow|x-slow|(?:(\+|-)[0-9.]+%))["][>]|[<][/]prosody[>]/g,
};

const SYNTHESIS_TYPE = {
  SINGLE_VOICE: 'SINGLE_VOICE',
  MULTI_VOICE: 'MULTI_VOICE',
};

const API_RESPONSE_TYPE = {
  STUDIO_API: 'STUDIO_API',
  V3_ENTERPRISE_DIRECT: 'V3_ENTERPRISE_DIRECT',
  V3_ENTERPRISE_INDIRECT: 'V3_ENTERPRISE_INDIRECT',
  V3_PERSONAL: 'V3_PERSONAL',
  V3_ARTICLE: 'V3_ARTICLE',
};

const V3_API_TYPE = {
  ENTERPRISE: 'ENTERPRISE',
  PERSONAL: 'PERSONAL',
  ARTICLE: 'ARTICLE',
};

const SERVICE_TYPE = {
  AI_VOICE: 'AI_VOICE',
};

const DEFAULT_RETENTION_PERIOD = 5;

const TIME_DELTA_REGEX =
  /^(?:[0-9]{2}:[0-5][0-9]:[0-5][0-9](?:[.,][0-9]{1,3})?)$/; // Eg: 00:59:59,000 or 00:59:59

const ACRONYM_LINKS = ['&', '-', '.', '–'];
const WORD_LINKS = ['và', '-', ',', '&', '–'];

const STORAGE = {
  S3: 'S3',
  GCS: 'GCS',
};

const VOICE_TYPE = {
  ZERO_SHOT: 'zero-shot',
  TTS: 'tts',
  CLONING: 'voice-cloning',
};

module.exports = {
  KAFKA_TOPIC,
  REQUEST_TYPE,
  VOICE_LEVEL,
  VOICE_PROVIDER,
  NON_VBEE_PROVIDERS,
  AUDIO_TYPE,
  REQUEST_STATUS,
  TTS_STATUS,
  EMPHASIS_LEVEL,
  RESPONSE_TYPE,
  TTS_CORE_VERSION,
  TTS_CORE_COMPUTE_PLATFORM,
  OUTPUT_TYPE,
  VALID_SAMPLE_RATE,
  VALID_BIT_RATE,
  LOADING_SYNTHESIS_FACTOR,
  VALID_SPEED,
  VALID_BACKGROUND_MUSIC_VOLUME,
  REDIS_KEY_PREFIX,
  REDIS_KEY_TTL,
  REGEX,
  VALID_CHARACTERS_LENGTH_REGEX,
  SYNTHESIS_TYPE,
  API_RESPONSE_TYPE,
  V3_API_TYPE,
  SERVICE_TYPE,
  DEFAULT_RETENTION_PERIOD,
  TIME_DELTA_REGEX,
  ACRONYM_LINKS,
  WORD_LINKS,
  STORAGE,
  VOICE_TYPE,
};

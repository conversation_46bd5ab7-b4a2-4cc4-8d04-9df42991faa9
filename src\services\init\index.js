const uuid = require('uuid');
const logger = require('../../utils/logger');
const { loadFeaturesRealtime } = require('../growthbook');

global.logger = logger;
global.KAFKA_CONSUMER_GROUP_RANDOM_TTS = uuid.v4();
global.VOICES = null;
global.LANGUAGES = null;
global.REQUEST_DIRECT = {};
global.REQUEST_DIRECT = {};
global.AWS_ZONES_TTS_CACHING = {};
global.AWS_ZONES_TTS_STUDIO = {};
global.APPS = {};

require('../../models');
require('../kafka');
require('../redis');

require('./voices');
require('./apps');
require('./awsZone');
require('./initPublicKey');

loadFeaturesRealtime();

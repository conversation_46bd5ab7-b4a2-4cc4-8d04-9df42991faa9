PORT=

ENVIRONMENT=
MONGO_URI=
REDIS_URI=

IAM_URL=
IAM_REALM=
IAM_VALID_CLIENT_IDS=
ENV=

KAFKA_CLIENT_ID=
KAFKA_BROKERS=

PRIVATE_RSA_KEY=

STUDIO_TTS_VERSION_DEFAULT=1.0

LOG_SHORTEN=

GET_APPS_INTERVAL=300000, // 5 minutes

GROWTH_BOOK_API_HOST=
GROWTH_BOOK_CLIENT_KEY=
LOADING_FEATURES_REALTIME_INTERVAL=1000 // Miliseconds

VC_SYNTHESIS_FUNCTION=
VC_ZERO_SHOT_CLOUD_RUN_URL=

IS_TTS_GATE="true"
TTS_GATE_API_URL="http://localhost:80"

TTS_CORE_URL=""
TTS_CORE_APP_ID=""
TTS_CORE_ACCESS_TOKEN=""

module.exports = {
  PORT: process.env.PORT || 80,
  <PERSON>ON<PERSON><PERSON>_URI: process.env.MONGO_URI,
  REDIS_URI: process.env.REDIS_URI,
  IAM_URL: process.env.IAM_URL,
  IAM_REALM: process.env.IAM_REALM,
  IAM_VALID_CLIENT_IDS: process.env.IAM_VALID_CLIENT_IDS
    ? process.env.IAM_VALID_CLIENT_IDS.split(',')
    : [],

  NODE_ENV: process.env.NODE_ENV,
  ENV: process.env.ENV || 'dev',
  PRODUCT: process.env.PRODUCT,
  SERVER_ENV: process.env.SERVER_ENV,
  KAFKA_CONSUMER_GROUP_TTS: 'tts-api-consumer',

  KAFKA_BROKERS: process.env.KAFKA_BROKERS.split(',') || [],
  KAFKA_CLIENT_ID: process.env.KAFKA_CLIENT_ID || 'kafka-client',

  IS_TTS_GATE: process.env.IS_TTS_GATE === 'true',
  TTS_GATE_API_URL: process.env.TTS_GATE_API_URL,

  TTS_CORE_URL: process.env.TTS_CORE_URL,
  TTS_CORE_APP_ID: process.env.TTS_CORE_APP_ID,
  TTS_CORE_ACCESS_TOKEN: process.env.TTS_CORE_ACCESS_TOKEN,

  GET_APPS_INTERVAL: parseInt(process.env.CHECK_REQUEST_INTERVAL, 10) || 300000, // 5 minutes,
  PRIVATE_RSA_KEY: process.env.PRIVATE_RSA_KEY?.replace(/\\n/g, '\n'),

  DEFAULT_AWS_REGION: process.env.DEFAULT_AWS_REGION,
  DEFAULT_NORMALIZER_FUNCTION: process.env.DEFAULT_NORMALIZER_FUNCTION,
  DEFAULT_SENTENCE_TOKENIZER_FUNCTION:
    process.env.DEFAULT_SENTENCE_TOKENIZER_FUNCTION,
  DEFAULT_NEW_SENTENCE_TOKENIZER_FUNCTION:
    process.env.DEFAULT_NEW_SENTENCE_TOKENIZER_FUNCTION,
  DEFAULT_T2A_FUNCTION: process.env.DEFAULT_T2A_FUNCTION,
  DEFAULT_SYNTHESIS_FUNCTION: process.env.DEFAULT_SYNTHESIS_FUNCTION,
  DEFAULT_JOIN_AUDIO_FUNCTION: process.env.DEFAULT_JOIN_AUDIO_FUNCTION,
  DEFAULT_BUCKET_S3: process.env.DEFAULT_BUCKET_S3,
  MULTI_ZONE: parseInt(process.env.MULTI_ZONE, 10) || 0,
  STUDIO_TTS_VERSION_DEFAULT: process.env.STUDIO_TTS_VERSION_DEFAULT,

  LOG_SHORTEN: JSON.parse(process.env.LOG_SHORTEN ?? false),
  SYNTHESIS_TTS_API: JSON.parse(process.env.SYNTHESIS_TTS_API ?? true),

  GROWTH_BOOK_API_HOST: process.env.GROWTH_BOOK_API_HOST,
  GROWTH_BOOK_CLIENT_KEY: process.env.GROWTH_BOOK_CLIENT_KEY,
  LOADING_FEATURES_REALTIME_INTERVAL:
    parseInt(process.env.LOADING_FEATURES_REALTIME_INTERVAL, 10) || 1, // minutes
  VC_SYNTHESIS_FUNCTION: process.env.VC_SYNTHESIS_FUNCTION,
  VC_ZERO_SHOT_CLOUD_RUN_URL: process.env.VC_ZERO_SHOT_CLOUD_RUN_URL || '',
};

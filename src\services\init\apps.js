require('dotenv').config();
require('../../models');
const { GET_APPS_INTERVAL } = require('../../configs');
const { getAllApps } = require('../../daos/app');

const initApps = async () => {
  try {
    const apps = (await getAllApps()) || [];

    apps.forEach((app) => {
      APPS[app._id] = app;
    });
  } catch (error) {
    logger.error(error, { ctx: 'InitApps' });
  } finally {
    setTimeout(initApps, GET_APPS_INTERVAL);
  }
};

initApps();

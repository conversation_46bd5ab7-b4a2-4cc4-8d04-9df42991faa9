const NodeRSA = require('node-rsa');
const aesjs = require('aes-js');
const { PRIVATE_RSA_KEY } = require('../configs');

const decryptTags = ({ encryptedTags, aesKey, requestId, sessionId }) => {
  const key = new NodeRSA(PRIVATE_RSA_KEY);

  key.setOptions({ encryptionScheme: 'pkcs1', environment: 'browser' });
  const aesKeyString = key.decrypt(aesKey, 'utf8');
  const aesTtsKey = aesjs.utils.utf8.toBytes(aesKeyString);
  const decryptedTags = {};
  for (const tag in encryptedTags) {
    if (tag.indexOf('ne_') === -1) {
      try {
        const encryptedText = encryptedTags[tag];
        const encryptedBytes = aesjs.utils.hex.toBytes(encryptedText);
        // eslint-disable-next-line new-cap
        const aesCtr = new aesjs.ModeOfOperation.ctr(
          aesTtsKey,
          new aesjs.Counter(5),
        );
        const decryptedBytes = aesCtr.decrypt(encryptedBytes);
        const decryptedText = aesjs.utils.utf8.fromBytes(decryptedBytes);
        decryptedTags[tag] = decryptedText;
      } catch (error) {
        logger.error(error, { ctx: 'DecryptTags', requestId, sessionId });
      }
    } else {
      decryptedTags[tag] = encryptedTags[tag];
    }
  }

  return decryptedTags;
};

module.exports = { decryptTags };

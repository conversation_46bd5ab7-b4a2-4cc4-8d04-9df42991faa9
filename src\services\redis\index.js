const redis = require('redis');

const { REDIS_URI } = require('../../configs');
const { REDIS_KEY_TTL } = require('../../constants');

const redisClient = redis.createClient({ url: REDIS_URI });

redisClient.on('error', (err) => {
  logger.error(`Connect error to Redis: ${REDIS_URI}`, {
    ctx: 'Redis',
    stack: err.stack,
  });
});

redisClient
  .connect()
  .then(() =>
    logger.info(`Connected to Redis: ${REDIS_URI}`, { ctx: 'Redis' }),
  );

const setKeyWithTtl = async (key, value, ttl = REDIS_KEY_TTL.DEFAULT) => {
  await redisClient.set(key, value, { EX: ttl });
};

const incrementKey = async (key) => {
  const number = await redisClient.incr(key);
  return number;
};

const decrementKey = async (key) => {
  const number = await redisClient.decr(key);
  if (number < 0) {
    await redisClient.set(key, 0);
  }
};

const setKeyIfNotExists = async (key, value) => {
  const res = await redisClient.setNX(key, value);
  return res;
};

module.exports = {
  redisClient,
  setKeyWithTtl,
  incrementKey,
  decrementKey,
  setKeyIfNotExists,
};

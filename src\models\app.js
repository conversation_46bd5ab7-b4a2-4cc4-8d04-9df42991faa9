const mongoose = require('mongoose');
const { SERVER_ENV } = require('../configs');
const { REQUEST_TYPE } = require('../constants');

const appSchema = new mongoose.Schema(
  {
    _id: String,
    name: String,
    token: String,
    expiresAt: Date,
    secretKey: String,
    active: { type: Boolean, default: true },
    server: { type: String, default: SERVER_ENV },
    allowRequestTypes: [{ type: String, enum: Object.values(REQUEST_TYPE) }],
    proxyDomain: String,
    voiceCodes: [String],
  },
  {
    _id: false,
    versionKey: false,
    timestamps: true,
  },
);

module.exports = mongoose.model('App', appSchema);

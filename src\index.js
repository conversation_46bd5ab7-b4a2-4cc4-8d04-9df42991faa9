require('dotenv').config();
const express = require('express');
const path = require('path');
const compression = require('compression');
const cors = require('cors');
const helmet = require('helmet');

const camelCaseReq = require('./middlewares/camelCaseReq');
const omitReq = require('./middlewares/omitReq');
const snakeCaseRes = require('./middlewares/snakeCaseRes');
const errorHandler = require('./middlewares/errorHandler');
const successReq = require('./middlewares/successReq');
const getIp = require('./middlewares/getIp');

const app = express();

app.use(cors());
app.use(helmet());
app.use(compression());
app.use(express.json({ limit: '10MB' }));
app.use(express.urlencoded({ extended: true }));
app.use(camelCaseReq);
app.use(omitReq);
app.use(snakeCaseRes());
app.use(successReq());
app.use(getIp);
app.use(express.static(path.join(__dirname, '..', 'public')));

require('./services/init');
require('./routes')(app);

app.use(errorHandler);

const { PORT } = require('./configs');

app.listen(PORT, () => {
  logger.info(`Server is running on port ${PORT}`);
});

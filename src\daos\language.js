const Language = require('../models/language');
const daoUtils = require('./utils');

const createLanguages = async (languages) => {
  await Language.insertMany(languages);
};

const findLanguages = async (query = {}) => {
  const { documents: languages, total } = await daoUtils.find(Language, query);
  return {
    languages: languages.map((language) => {
      delete language._id;
      return language;
    }),
    total,
  };
};

module.exports = { createLanguages, findLanguages };

require('../../models');
const { REQUEST_TYPE } = require('../../constants');
const { getAwsZones } = require('../../daos/awsZone');

const createArrayFollowWeight = (awsZones) => {
  const arrayFollowWeight = [];
  awsZones.forEach(({ region, weight }) => {
    for (let i = 0; i < weight; i += 1) {
      arrayFollowWeight.push(region);
    }
  });
  return arrayFollowWeight;
};

const initTtsCachingAwsZone = async () => {
  const awsZones = (await getAwsZones()) || [];
  const filteredZones = awsZones.filter(({ allowRequestTypes }) =>
    allowRequestTypes.includes(REQUEST_TYPE.API_CACHING),
  );
  const arrayFollowWeight = createArrayFollowWeight(filteredZones);
  global.AWS_ZONES_TTS_CACHING = arrayFollowWeight;
};

const initTtsStudioAwsZone = async () => {
  const awsZones = (await getAwsZones()) || [];
  const filteredZones = awsZones.filter(({ allowRequestTypes }) =>
    allowRequestTypes.includes(REQUEST_TYPE.STUDIO),
  );
  const arrayFollowWeight = createArrayFollowWeight(filteredZones);
  global.AWS_ZONES_TTS_STUDIO = arrayFollowWeight;
};

module.exports = { initTtsCachingAwsZone };

initTtsCachingAwsZone();
initTtsStudioAwsZone();

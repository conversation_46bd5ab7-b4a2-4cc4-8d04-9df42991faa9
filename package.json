{"name": "vbee-tts-stats", "version": "1.0.0", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "postinstall": "husky install"}, "author": "", "license": "ISC", "dependencies": {"@growthbook/growthbook": "^0.31.0", "@kubernetes/client-node": "^0.20.0", "@vbee-holding/node-logger": "^2.0.0", "aes-js": "^3.1.2", "axios": "0.24.0", "camelcase-keys": "7.0.1", "chardet": "^2.0.0", "cheerio": "^1.0.0-rc.12", "compression": "1.7.4", "cors": "2.8.5", "cron": "^2.2.0", "cross-env": "7.0.3", "cross-fetch": "^4.0.0", "dotenv": "10.0.0", "eventsource": "^2.0.2", "express": "4.17.1", "express-mung": "0.5.1", "express-validation": "3.0.8", "franc": "^6.1.0", "helmet": "4.6.0", "husky": "7.0.4", "iconv-lite": "^0.6.3", "jsonwebtoken": "8.5.1", "kafkajs": "^2.2.4", "md5": "^2.3.0", "moment": "^2.29.4", "mongoose": "7.7.0", "morgan": "1.10.0", "node-rsa": "^1.1.1", "nodemon": "^2.0.22", "redis": "^4.6.5", "retry": "^0.13.1", "snakecase-keys": "5.1.0", "uuid": "^8.3.2", "yargs": "^17.7.2"}, "devDependencies": {"eslint": "7.32.0", "eslint-config-airbnb-base": "14.2.1", "eslint-config-prettier": "8.3.0", "eslint-plugin-import": "2.25.2", "eslint-plugin-prettier": "4.0.0", "lint-staged": "11.2.6", "prettier": "2.4.1"}, "lint-staged": {"*.js": "eslint"}}
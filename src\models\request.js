const mongoose = require('mongoose');
require('dotenv').config();
const { SERVER_ENV } = require('../configs');

const {
  AUDIO_TYPE,
  REQUEST_STATUS,
  RESPONSE_TYPE,
  REQUEST_TYPE,
  TTS_CORE_VERSION,
  TTS_CORE_COMPUTE_PLATFORM,
  OUTPUT_TYPE,
  V3_API_TYPE,
} = require('../constants');
const { VALID_CLIENT_PAUSE } = require('../constants/clientPause');

const requestSchema = new mongoose.Schema(
  {
    _id: String,
    title: String,
    text: String,
    voiceCode: String,
    characters: Number,
    sentences: [
      {
        _id: false,
        text: String,
        voiceCode: String,
        speed: Number,
        breakTime: Number,
        characters: Number,
        elements: [
          {
            _id: false,
            key: String,
            text: String,
            startOffset: Number,
            endOffset: Number,
            name: String,
            value: String,
          },
        ],
      },
    ],
    audioType: { type: String, enum: Object.values(AUDIO_TYPE) },
    backgroundMusic: {
      name: String,
      link: String,
      volume: Number,
    },
    volume: Number,
    speed: Number,
    audioLink: String,
    bitrate: Number,
    sampleRate: String,
    userId: String,
    status: {
      type: String,
      enum: Object.values(REQUEST_STATUS),
      default: REQUEST_STATUS.IN_PROGRESS,
    },
    createdAt: Date,
    endedAt: Date, // time ended process request
    usedCharacters: {
      paid: Number,
      bonus: Number,
    },
    isFromCache: { type: Boolean, default: false },
    paid: { type: Boolean, default: false },
    refund: { type: Boolean, default: false },
    progress: { type: Number, default: 0 },
    demo: { type: Boolean, default: false },
    error: String,
    errorCode: String,
    retentionPeriod: Number,
    sentenceTokenizerDuration: Number,
    t2aDuration: { min: Number, max: Number, average: Number },
    synthesisDuration: {
      min: Number,
      max: Number,
      average: Number,
      start: Number,
      end: Number,
    },
    joinAudioDuration: Number,
    type: { type: String, enum: Object.values(REQUEST_TYPE) },
    responseType: { type: String, enum: Object.values(RESPONSE_TYPE) },
    app: { type: String, ref: 'App' },
    callbackUrl: String,
    callbackUpdateProgressUrl: String,
    ip: String,
    version: { type: String, enum: Object.values(TTS_CORE_VERSION) },
    synthesisComputePlatform: {
      type: String,
      enum: Object.values(TTS_CORE_COMPUTE_PLATFORM),
    },
    outputType: {
      type: String,
      enum: Object.values(OUTPUT_TYPE),
      default: OUTPUT_TYPE.LINK,
    },
    v3ApiType: {
      type: String,
      enum: Object.values(V3_API_TYPE),
    },
    awsZoneSynthesis: String,
    awsZoneFunctions: {
      normalizerFunction: { type: String },
      sentenceTokenizerFunction: { type: String },
      newSentenceTokenizerFunction: { type: String },
      textToAllophoneFunction: { type: String },
      synthesisFunction: { type: String },
      joinSentencesFunction: { type: String },
      srtFunction: { type: String },
      s3Bucket: { type: String },
    },
    fromVn: Boolean,
    template: String,
    tags: String,
    aesKey: String, // Use for encrypting value in tags, that were hashed. this key get from request
    cachingRate: { type: Number, default: 0 }, // Conversion rate of characters using cache
    reusedRate: { type: Number, default: 0 }, // reused audio rate
    cachingTime: Number, // Time process request from caching tts system
    cachingMessage: String, // Message from TTS Caching system
    cachingStatusCode: Number,
    sessionId: String, // Used to trace calls, corresponding to callId
    server: { type: String, default: SERVER_ENV },
    hidden: { type: Boolean, default: false },
    serviceType: String,
    clientUserId: String,
    dictionary: [{ word: String, pronunciation: String, _id: false }],
    clientPause: {
      paragraphBreak: {
        type: Number,
      },
      // .
      sentenceBreak: {
        type: Number,
        min: VALID_CLIENT_PAUSE.MIN,
        max: VALID_CLIENT_PAUSE.MAX,
      },
      // ;
      majorBreak: {
        type: Number,
        min: VALID_CLIENT_PAUSE.MIN,
        max: VALID_CLIENT_PAUSE.MAX,
      },
      // ,
      mediumBreak: {
        type: Number,
        min: VALID_CLIENT_PAUSE.MIN,
        max: VALID_CLIENT_PAUSE.MAX,
      },
    },
    synthesisCcr: Number,
    timeProcessCore: {
      processDuration: Number,
      stepPreprocess: Number,
      stepText2Phones: Number,
      stepInfer: Number,
      stepPostprocess: Number,
    },
    timeProcess: {
      clientSendRequestAt: Date,
      receivedAt: Date,
      endAuthProcessAt: Date,
      endPreCheckAt: Date,
      endUploadBackgroundMusicAt: Date,
      endCheckVoiceAt: Date,
      sendRequestSynthesisAt: Date,
      getSynthesisRequestAt: Date,
      startInvokeLambdaFunctionAt: Date,
      endInvokeLambdaFunctionAt: Date,
      getSynthesisResponseAt: Date,
      preSendResponseAt: Date,
      startSendResponseAt: Date,
      sendResponseEndAt: Date,
      processDuration: Number,
      stepPreProcessDuration: Number,
      stepWorkerDuration: Number,
    },
    proxyDomain: String,
    sentencesVoiceCode: {},
    returnTimestamp: { type: Boolean, default: false },
    isPriority: { type: Boolean, default: false },
    isRealTime: { type: Boolean, default: false },
  },
  {
    _id: false,
    timestamps: { updatedAt: true },
    versionKey: false,
  },
);

module.exports = mongoose.model('Request', requestSchema);
